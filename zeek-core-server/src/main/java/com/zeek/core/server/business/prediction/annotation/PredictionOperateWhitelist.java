package com.zeek.core.server.business.prediction.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 预测市场操作白名单权限控制注解
 * 用于控制创建市场、关闭市场、创建结果等操作的权限
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PredictionOperateWhitelist {
    
    /**
     * 自定义错误消息
     * @return 错误消息
     */
    String message() default "操作不被允许，用户不在操作白名单中";
}
