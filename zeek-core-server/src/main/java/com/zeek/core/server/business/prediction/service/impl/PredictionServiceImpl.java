package com.zeek.core.server.business.prediction.service.impl;

import cn.hutool.core.util.EnumUtil;
import co.evg.scaffold.innerEvent.client.InnerEventClient;
import co.evg.scaffold.innerEvent.common.model.InnerEventDTO;
import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.kikitrade.kseq.api.SeqClient;
import com.zeek.core.api.*;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.constants.ZeekConstants.MarketEvent;
import com.zeek.core.common.constants.ZeekConstants.MarketStatus;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.common.statemachine.StateMachine;
import com.zeek.core.common.util.ContractUtils;
import com.zeek.core.common.util.Create2CloneCalculator;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.eip712.PredictionCloseSignatureUtils;
import com.zeek.core.contract.eip712.message.PayoutDataTypedMessage;
import com.zeek.core.contract.model.event.prediction.*;
import com.zeek.core.dal.ots.builder.*;
import com.zeek.core.dal.ots.model.*;
import com.zeek.core.server.business.contract.siger.ISigner;
import com.zeek.core.server.business.prediction.action.*;
import com.zeek.core.server.business.prediction.model.MarketActionContext;
import com.zeek.core.server.business.prediction.model.PredictionModelConvert;
import com.zeek.core.server.business.prediction.oracle.PredictionOracleFactory;
import com.zeek.core.server.business.prediction.oracle.PredictionOracleService;
import com.zeek.core.server.business.prediction.service.*;
import com.zeek.core.server.business.prediction.annotation.PredictionOperateWhitelist;
import com.zeek.core.server.business.reference.service.IQuestsReference;
import com.zeek.core.server.common.seq.VoteSeqRuleBuilder;
import com.zeek.core.server.configuration.ChainConfigLoader;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zeek.core.common.constants.ZeekConstants.MarketEvent.ZEEK_EXPIRED_MARKET;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Ended;
import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.RECEIVE;
import static com.zeek.core.server.common.constants.QuestsConstants.PointBusinessType.PREDICTION_VOTE_WIN;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 19:04
 */
@Service
@Slf4j
public class PredictionServiceImpl implements PredictionService {

    @Resource
    PredictionMarketProperties predictionMarketProperties;
    @Resource
    StateMachine<MarketStatus, MarketEvent> stateMachine;
    @Resource
    SeqClient seqClient;
    @Resource
    Map<String, ISigner> signers;
    @Resource
    IQuestsReference questsReference;
    @Resource
    PredictionMarketsService predictionMarketsService;
    @Resource
    PredictionOutcomesService predictionOutcomesService;
    @Resource
    PredictionPositionsService predictionPositionsService;
    @Resource
    PredictionVoteService predictionVoteService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    PredictionMarketsBuilder predictionMarketsBuilder;
    @Resource
    ProfileBasicBuilder profileBasicBuilder;
    @Resource
    private PredictionVotesBuilder predictionVotesBuilder;
    @Resource
    InnerEventClient innerEventClient;
    @Autowired
    private ChainConfigLoader chainConfigLoader;
    @Resource
    private PredictionOracleFactory predictionOracleFactory;

    @PostConstruct
    void buildStateMachine() {
        stateMachine.accept(null, MarketEvent.ZEEK_CREATE_MARKET, CreateMarketEventAction.class);
        stateMachine.accept(MarketStatus.Pending, MarketEvent.ZEEK_ISSUED_MARKET_ON_CHAIN, ActiveMarketEventAction.class);
        stateMachine.accept(MarketStatus.Running, ZEEK_EXPIRED_MARKET, ExpiredMarketEventAction.class);
        stateMachine.accept(Ended, MarketEvent.ZEEK_CLOSED_MARKET, ClosedMarketEventAction.class);
    }

    @Override
    public PredictionConfigDTO configs() {
        if (predictionMarketProperties == null) {
            return null;
        }

        PredictionConfigDTO.Builder builder = PredictionConfigDTO.newBuilder();
        List<PredictionMarketProperties.TokenConfig> tokens = predictionMarketProperties.getTokens();
        if (CollectionUtils.isNotEmpty(tokens)) {
            tokens.forEach(tokenConfig -> {
                PredictionTokenConfig.Builder tokenBuilder = PredictionTokenConfig.newBuilder()
                        .setName(tokenConfig.getName())
                        .setSymbol(tokenConfig.getSymbol())
                        .setAddress(tokenConfig.getAddress())
                        .setCollateralMin(tokenConfig.getCollateralMin())
                        .setCollateralMax(tokenConfig.getCollateralMax())
                        .setSlotUnit(tokenConfig.getSlotUnit());
                builder.addTokens(tokenBuilder.build());
            });
        }

        PredictionMarketProperties.DaysConfig days = predictionMarketProperties.getDays();
        if (days != null) {
            PredictionDaysConfig.Builder daysBuilder = PredictionDaysConfig.newBuilder()
                    .setMin(days.getMin())
                    .setMax(days.getMax());
            builder.setDays(daysBuilder.build());
        }

        PredictionMarketProperties.FeeConfig fee = predictionMarketProperties.getFee();
        if (fee != null) {
            PredictionFeeConfig.Builder feeBuilder = PredictionFeeConfig.newBuilder()
                    .setTotal(fee.getTotal())
                    .setPlatform(fee.getPlatform())
                    .setPoster(fee.getPoster())
                    .setAnswer(fee.getAnswer());
            builder.setFee(feeBuilder.build());
        }

        PredictionMarketProperties.VoteConfig vote = predictionMarketProperties.getVote();
        if (vote != null) {
            PredictionVoteConfig.Builder voteBuilder = PredictionVoteConfig.newBuilder()
                    .setMin(vote.getMin())
                    .setMax(vote.getMax())
                    .addAllOptions(vote.getOptions());
            builder.setVote(voteBuilder.build());
        }

        return builder.build();
    }

    public String getConditionId(String questionId, Long slot, Long expiredAt, Long margin, String customerId) {
        BigInteger saltNonce = BigInteger.ZERO;

        String implementationMaster = predictionMarketProperties.getImplementationMaster();
        ProfileBasicDO profileBasicDO = new ProfileBasicDO();
        profileBasicDO.setCustomerId(customerId);
        profileBasicDO = profileBasicBuilder.getRow(profileBasicDO);
        String msgSender = profileBasicDO.getAddress();
        String factoryAddress = chainConfigLoader.getChainConfig().getContracts().get("LMSRMarketFactory").getAddress();
        String conditionalToken = chainConfigLoader.getChainConfig().getContracts().get("ConditionalToken").getAddress();
        log.info("get condition id params is {} {} {} {} {} {} {} {}", msgSender, factoryAddress, conditionalToken, implementationMaster, questionId, slot, expiredAt, margin);
        byte[] constructorData = Create2CloneCalculator.encodeConstructorData(
                conditionalToken, // pmSystem
                questionId, // questionId
                BigInteger.valueOf(slot), // outcomeSlotCount
                BigInteger.valueOf(expiredAt / 1000), // expiredAt
                BigInteger.valueOf(margin) // margin
        );
        String predictedAddress = Create2CloneCalculator.predictCreate2Address(
                factoryAddress,
                implementationMaster,
                saltNonce,
                msgSender,
                constructorData
        );
        String conditionId = ContractUtils.getConditionId(predictedAddress, questionId, BigInteger.valueOf(slot));
        return conditionId;
    }

    @Override
    public PredictionMarketDTO createMarket(CreatMarketRequest request) throws BaseException {
        try {
            // 创建上下文
            String questionId = Numeric.toHexString(Hash.sha3(request.getTitle().getBytes()));
            String oracle = getSigner(ZeekConstants.QUEST_VERIFY + ZeekConstants.SIGNER).getSignerAddress();
            Long slot = request.getSlot();

            String conditionId = getConditionId(questionId, slot, request.getEndTime(), (long)(Double.parseDouble(request.getValue()) * 1000000000000000000L), request.getCustomerId());

            PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
            if (marketDO != null) {
                if (MarketStatus.Pending.name().equals(marketDO.getStatus()) && marketDO.getCustomerId().equals(request.getCustomerId())) {
                    return PredictionModelConvert.toMarketDTO(marketDO);
                }
                throw new BaseException(ZeekCoreRespondCode.MARKET_ALREADY_EXIST);
            }

            MarketActionContext context = MarketActionContext.builder()
                    .id(conditionId)
                    .conditionId(conditionId)
                    .customerId(request.getCustomerId())
                    .questionId(questionId)
                    .slot(slot)
                    .title(request.getTitle())
                    .content(request.getContent())
                    .medias(request.getMediasList())
                    .token(request.getToken())
                    .value(request.getValue())
                    .endTime(request.getEndTime())
                    .created(System.currentTimeMillis())
                    .oracleType(EnumUtil.fromString(ZeekConstants.PredictionOracleType.class, request.getOracleType(), predictionMarketProperties.getOracle().getType()))
                    .build();

            // 触发状态机
            boolean result = stateMachine.action(null, MarketEvent.ZEEK_CREATE_MARKET, context);
            if (result) {
                // 获取创建的市场
                marketDO = predictionMarketsBuilder.getRowByConditionId(context.getConditionId());

                // 转换为DTO返回
                return PredictionModelConvert.toMarketDTO(marketDO);
            }
        } catch (BaseException e) {
            log.error("Failed to create market", e);
            throw e;
        }

        return null;
    }

    @Override
    public boolean handleMarketOnChain(MarketAMMCreatedEventLog marketAMMCreatedEventLog) throws BaseException {
        // 将字节数组转换为16进制字符串
        StringBuilder hexString = new StringBuilder("0x");
        for (byte b : marketAMMCreatedEventLog.getConditionId()) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String conditionId = hexString.toString();

        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketDO == null) {
            log.error("Market not found: {}", conditionId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 创建上下文
        MarketActionContext context = MarketActionContext.builder()
                .id(conditionId)
                .eventLog(marketAMMCreatedEventLog)
                .conditionId(marketDO.getConditionId())
                .customerId(marketDO.getCustomerId())
                .chainId(marketDO.getChainId())
                .marketAddress(marketAMMCreatedEventLog.getMarketAddress())
                .questionId(Arrays.toString(marketAMMCreatedEventLog.getQuestionId()))
                .slot(marketAMMCreatedEventLog.getOutcomeSlotCount().longValue())
                .fee(String.valueOf(marketAMMCreatedEventLog.getFee()))
                .creatorFee(String.valueOf(marketAMMCreatedEventLog.getCreatorFee()))
                .proposalFee(String.valueOf(marketAMMCreatedEventLog.getProposalFee()))
                .token(marketAMMCreatedEventLog.collateralToken)
                .adminAddress(marketAMMCreatedEventLog.getAdminAddress())
                .financeAddress(marketAMMCreatedEventLog.getFinanceAddress())
                .oracleAddress(marketAMMCreatedEventLog.getOracleAddress())
                .collateralUnit(marketAMMCreatedEventLog.getCollateralUnit())
                .build();

        // 触发状态机
        return stateMachine.action(MarketStatus.get(marketDO.getStatus()),
                MarketEvent.ZEEK_ISSUED_MARKET_ON_CHAIN,
                context);
    }

    @Override
    public boolean handleAddFundingOnChain(MarketAMMFundingAddedEventLog eventLog) throws BaseException {
        log.info("market outcome processOnChain eventLog:{}", JSON.toJSONString(eventLog));
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getByMarketAddress(eventLog.getMarketAddress());
        if (marketsDO == null) {
            log.warn("market not exist, marketAddress: {}", eventLog.getMarketAddress());
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_EXIST);
        }

        ProfileBasicDO proposalProfile = profileBasicBuilder.getByAddress(eventLog.getProposal());
        if (proposalProfile == null) {
            log.warn("proposal profile not exist, proposalAddress: {}", eventLog.getProposal());
            throw new BaseException(ZeekCoreRespondCode.PROFILE_NOT_EXIST);
        }

        // 修改 outcome 状态
        PredictionOutcomesDO outcomesDO = predictionOutcomesService.activeOutcomes(marketsDO.getConditionId(), proposalProfile.getCustomerId(), marketsDO.getToken(), eventLog);
        if (outcomesDO == null) {
            log.error("market outcome update status to active fail, marketId: {}, proposalAddress: {}", marketsDO.getConditionId(), eventLog.getProposal());
            throw new BaseException(ZeekCoreRespondCode.MARKET_OUTCOME_UPDATE_FAILED);
        }

        // 更新 market 池子大小
        boolean b = predictionMarketsService.updateFunding(marketsDO.getConditionId(), eventLog.getFundingPool(), eventLog.getTimestamp());
        if (!b) {
            log.error("market update funding fail, marketId: {}, funding: {}, timestamp: {}", marketsDO.getConditionId(), eventLog.getFundingPool(), eventLog.getTimestamp());
            throw new BaseException(ZeekCoreRespondCode.MARKET_UPDATE_FAILED);
        }

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_FUNDING_ADDED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_FUNDING_ADDED)
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.MARKET_OUTCOME, outcomesDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_FUNDING_ADDED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.WALLET_TRANSACTION)
                .addBody(CoreInnerEventConstants.WALLET_TRANSACTION_TYPE, CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_OUTCOME)
                .addBody(CoreInnerEventConstants.AA_ADDRESS, proposalProfile.getAddress())
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));
        return true;
    }

    @Override
    public boolean handleTradeOnChain(MarketAMMTokenTradeEventLog eventLog) throws BaseException {
        log.info("market trade processOnChain eventLog:{}", JSON.toJSONString(eventLog));
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getByMarketAddress(eventLog.getMarketAddress());
        if (marketsDO == null) {
            log.warn("market not exist, marketAddress: {}", eventLog.getMarketAddress());
            return false;
        }

        ProfileBasicDO profileDO = profileBasicBuilder.getByAddress(eventLog.getTrader());
        if (profileDO == null) {
            log.warn("profile not exist, address: {}", eventLog.getTrader());
            return false;
        }

        // 更新 profile positions
        boolean pb = predictionPositionsService.updatePositions(profileDO.getCustomerId(), marketsDO.getConditionId(), eventLog.getOutcomeSlot().intValue(), eventLog.getAmount(), eventLog.getTimestamp().longValue());
        if (!pb) {
            log.warn("prediction update positions fail, customerId: {}, marketId: {}, slot: {}, amount: {}, timestamp: {}",
                    profileDO.getCustomerId(), marketsDO.getConditionId(), eventLog.getOutcomeSlot(), eventLog.getAmount(), eventLog.getTimestamp());
            return false;
        }

        // 更新 market funding
        boolean fb = predictionMarketsService.updateFunding(marketsDO.getConditionId(), eventLog.getFundingPool(), eventLog.getTimestamp());
        if (!fb) {
            log.warn("prediction update funding fail, marketId: {},funding: {}, timestamp: {}",
                    marketsDO.getConditionId(), eventLog.getFundingPool(), eventLog.getTimestamp());
            return false;
        }

        // 更新 outcome total_position
        boolean ob = predictionOutcomesService.updatePosition(marketsDO.getConditionId(), eventLog.getOutcomeSlot().intValue(), eventLog.getBalance(), eventLog.getTimestamp());
        if (!ob) {
            log.warn("prediction update position fail, marketId: {}, slot: {}, funding: {}",
                    marketsDO.getConditionId(), eventLog.getOutcomeSlot(), eventLog.getFundingPool());
            return false;
        }

        // 记录 prediction transactions 流水、手续费
        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_TOKEN_TRADE_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_TRADE)
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.CUSTOMER_ID, profileDO.getCustomerId())
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        // 记录钱包流水
        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_TOKEN_TRADE_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.WALLET_TRANSACTION)
                .addBody(CoreInnerEventConstants.WALLET_TRANSACTION_TYPE, CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_TRADE)
                .addBody(CoreInnerEventConstants.AA_ADDRESS, profileDO.getAddress())
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        log.info("market trade processOnChain success, transactionHash:{}", eventLog.getLog().getTransactionHash());
        return true;
    }

    @Override
    public boolean handleCloseMarketOnChain(MarketAMMClosedEventLog event) throws BaseException {
        String marketAddress = event.getMarketAddress();
        List<BigInteger> payouts = event.getPayouts();
        log.info("prediction handleCloseMarketOnChain: {}", event);

        // 获取市场记录
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getByMarketAddress(marketAddress);
        if (marketDO == null) {
            log.error("Market not found: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 创建上下文
        MarketActionContext context = MarketActionContext.builder()
                .id(marketDO.getConditionId())
                .conditionId(marketDO.getConditionId())
                .payouts(payouts.stream().map(BigInteger::intValue).collect(Collectors.toList()))
                .closedEventLog(event)
                .build();
        // 触发状态机
        return stateMachine.action(MarketStatus.get(marketDO.getStatus()),
                MarketEvent.ZEEK_CLOSED_MARKET,
                context);
    }

    @Override
    public boolean handleClaimOnChain(MarketAMMClaimedEventLog eventLog) throws BaseException {
        log.info("prediction handleClaimOnChain: {}", eventLog);
        String marketAddress = eventLog.getMarketAddress();
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getByMarketAddress(marketAddress);
        if (marketsDO == null) {
            log.error("Market not found: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        if (!MarketStatus.Closed.name().equals(marketsDO.getStatus())) {
            log.error("Market not closed: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_CLOSED);
        }

        ProfileBasicDO profileDO = profileBasicBuilder.getByAddress(eventLog.getParticipant());

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_CLAIMED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_CLAIM)
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.CUSTOMER_ID, profileDO.getCustomerId())
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_CLAIMED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.WALLET_TRANSACTION)
                .addBody(CoreInnerEventConstants.WALLET_TRANSACTION_TYPE, CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_CLAIM)
                .addBody(CoreInnerEventConstants.AA_ADDRESS, profileDO.getAddress())
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));
        return false;
    }

    @Override
    public boolean expiredPrediction(MarketActionContext context) throws BaseException {
        // 驱动prediction到ended状态
        log.info("expired prediction context:{}", JSON.toJSONString(context));
        return stateMachine.action(MarketStatus.Running, ZEEK_EXPIRED_MARKET, context);
    }

    @Override
    public CloseMarketResponse closeMarket(CloseMarketRequest request) throws BaseException, IOException, IllegalAccessException {
        PayoutDataTypedMessage message = new PayoutDataTypedMessage();
        PredictionMarketsDO predictionMarketsDO = predictionMarketsBuilder.getRowByConditionId(request.getMarketId());

        PredictionOracleService oracleService = predictionOracleFactory.getOracleService(predictionMarketsDO.getOracleType());
        List<BigInteger> marketResult = oracleService.getMarketResult(request.getMarketId(), request.getSlot());

        // 获取投票结果
        PredictionCloseSignatureUtils.PayoutsData data = new PredictionCloseSignatureUtils.PayoutsData();
        data.setConditionId(new Bytes32(Numeric.hexStringToByteArray(request.getMarketId())));
        data.setPayouts(marketResult);
        message.setConditionId(data.getConditionId());
        message.setPayoutIds(data.getPayouts());
        String signature = getSigner(ZeekConstants.QUEST_VERIFY + ZeekConstants.SIGNER)
                .signMessage(message.getMessageBytes(predictionMarketsDO.getMarketAddress(),
                        predictionMarketProperties.getMarketContractName(),
                        predictionMarketProperties.getMarketContractVersion(),
                        ChainIdUtil.getChainId().intValue(),
                        PredictionCloseSignatureUtils.encodePayoutsData(data)));

        // 构建响应
        CloseMarketResponse.Builder responseBuilder = CloseMarketResponse.newBuilder()
                .setMarketId(request.getMarketId())
                .setSignature(signature);

        // 将投票结果转换为字符串列表添加到payouts中
        for (BigInteger result : marketResult) {
            responseBuilder.addPayouts(result.toString());
        }

        log.info("Market closed successfully: {}, oracle type: {}, vote results: {}", request.getMarketId(), predictionMarketsDO.getOracleType(), marketResult);

        return responseBuilder.build();
    }

    /**
     * 生成积分发放缓存key
     *
     * @param marketId   市场ID
     * @param customerId 用户ID
     * @param slot       槽位
     * @return 缓存key
     */
    private String getPointSettleCacheKey(String marketId, String customerId, Integer slot) {
        return String.format("prediction:point:settle:%s:%s:%d", marketId, customerId, slot);
    }

    public ISigner getSigner(String type) {
        return signers.get(type);
    }


    @Override
    public boolean settleMarket(String marketId) throws BaseException {
        // TODO 1. 获取投票结果
        List<BigInteger> voteResults = predictionVoteService.getMarketVoteResult(marketId);
        if (voteResults == null || voteResults.isEmpty()) {
            log.warn("settleMarket: No vote results for marketId: {}", marketId);
            return false;
        }
        // 2. 找到所有获胜slot 与失败slot
        List<Integer> winnerSlots = new ArrayList<>();
        List<Integer> losingSlots = new ArrayList<>();
        for (int i = 0; i < voteResults.size(); i++) {
            if (BigInteger.ONE.equals(voteResults.get(i))) {
                winnerSlots.add(i);
            } else {
                losingSlots.add(i);
            }
        }
        if (winnerSlots.isEmpty()) {
            log.warn("settleMarket: No winner slot found for marketId: {}", marketId);
            return false;
        }
        // 3. 分页遍历获胜用户
        int limit = 100;
        int offset = 0;
        Double allPoint = predictionVotesBuilder.getMarketVote(marketId);
        Double winPoint = predictionVotesBuilder.getAnswerVote(marketId, winnerSlots);
        Double losePoint = predictionVotesBuilder.getAnswerVote(marketId, losingSlots);
        if (winPoint + losePoint != allPoint) {
            log.error("settleMarket: Abnormal voting score, please check: {} allPoint: {} winPoint: {} losePoint: {}", marketId, allPoint, winPoint, losePoint);
            return false;
        }
        for (Integer slot : winnerSlots) {
            while (true) {
                List<PredictionVotesDO> page = predictionVotesBuilder.getWinerPage(marketId, slot, String.valueOf(offset), limit);
                if (page == null || page.isEmpty()) {
                    break;
                }
                for (PredictionVotesDO votesDO : page) {
                    try {
                        // 先从Redis缓存查询是否已经发放过积分
                        String cacheKey = getPointSettleCacheKey(votesDO.getMarketId(), votesDO.getCustomerId(), slot);
                        String cachedPoint = redisTemplate.opsForValue().get(cacheKey);
                        if (StringUtils.isNotBlank(cachedPoint)) {
                            log.info("settleMarket: point already added from cache customer Id {} slot: {} point : {}", votesDO.getCustomerId(), slot, cachedPoint);
                            continue;
                        }

                        // 判断是否加过分了
                        Double addPoint = predictionVotesBuilder.getMarketSettle(votesDO.getMarketId(), votesDO.getCustomerId(), slot);
                        if (addPoint != null && addPoint > 0) {
                            log.info("settleMarket: point already added customer Id {} slot: {} point : {}", votesDO.getCustomerId(), slot, addPoint);
                            continue;
                        }
                        // 计算分数
                        BigDecimal userVoteValue = new BigDecimal(votesDO.getSumValue());
                        BigDecimal winPointDecimal = new BigDecimal(winPoint);
                        BigDecimal losePointDecimal = new BigDecimal(losePoint);

                        // amount = (votesDO.getValue()/winPoint) * losePoint + votesDO.getValue()
                        BigDecimal amount = userVoteValue.divide(winPointDecimal, 10, BigDecimal.ROUND_HALF_UP)
                                .multiply(losePointDecimal)
                                .add(userVoteValue);

                        // 向上取整转为整数
                        Long finalAmount = amount.setScale(0, BigDecimal.ROUND_HALF_UP).longValue();
                        // 加分处理
                        String businessId = seqClient.next(VoteSeqRuleBuilder.rule());
                        boolean result = questsReference.addPoint(votesDO.getCustomerId(), finalAmount, businessId, PREDICTION_VOTE_WIN, PREDICTION_VOTE_WIN.getDesc());
                        if (result) {
                            PredictionVotesDO predictionVotesDO = new PredictionVotesDO();
                            predictionVotesDO.setId(businessId);
                            predictionVotesDO.setCreated(System.currentTimeMillis());
                            predictionVotesDO.setChainId(votesDO.getChainId());
                            predictionVotesDO.setValue(finalAmount);
                            predictionVotesDO.setModified(System.currentTimeMillis());
                            predictionVotesDO.setCustomerId(votesDO.getCustomerId());
                            predictionVotesDO.setMarketId(votesDO.getMarketId());
                            predictionVotesDO.setSlot(slot);
                            predictionVotesDO.setDirection(RECEIVE.getCode());
                            predictionVotesBuilder.putRow(predictionVotesDO);
                            // 将积分发放结果缓存到Redis，缓存3分钟
                            redisTemplate.opsForValue().set(cacheKey, String.valueOf(finalAmount), 3, TimeUnit.MINUTES);
                            log.info("settleMarket: point added success marketId:{} slot:{} customer Id:{} allPoint:{}, winPoint:{} losePoint:{} votePoint:{} addPoint : {}", marketId, slot, votesDO.getCustomerId(), allPoint, winPointDecimal, losePointDecimal, userVoteValue, finalAmount);
                        } else {
                            log.info("settleMarket: point added failed marketId:{} slot:{} customer Id:{} allPoint:{}, winPoint:{} losePoint:{} votePoint:{} addPoint : {}", marketId, slot, votesDO.getCustomerId(), allPoint, winPointDecimal, losePointDecimal, userVoteValue, finalAmount);
                        }
                        // 记录分数
                    } catch (Exception e) {
                        log.error("settle vote result failed customerId {} marketId {} slot {}", votesDO.getCustomerId(), votesDO.getMarketId(), slot, e);
                    }
                }
                if (page.size() < limit) {
                    break;
                }
                offset += limit;
            }
        }
        return true;
    }

}
