package com.zeek.core.server.business.prediction.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.kikitrade.framework.common.model.TokenPage;
import com.zeek.core.api.*;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.contract.model.event.prediction.MarketAMMFeeChangedEventLog;
import com.zeek.core.dal.ots.builder.*;
import com.zeek.core.dal.ots.model.*;
import com.zeek.core.server.business.prediction.model.PredictionModelConvert;
import com.zeek.core.server.business.prediction.service.*;
import com.zeek.core.server.business.profile.model.GetAggProfileContext;
import com.zeek.core.server.business.profile.service.ProfileService;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Closed;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Ended;
import static com.zeek.core.common.constants.ZeekConstants.PredictionClaimStatus.CLAIMED;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 19:04
 */
@Service
@Slf4j
public class PredictionMarketsServiceImpl implements PredictionMarketsService {

    @Resource
    PredictionVoteService predictionVoteService;
    @Resource
    PredictionMarketsBuilder predictionMarketsBuilder;
    @Resource
    PredictionParticipantsBuilder predictionParticipantsBuilder;
    @Resource
    PredictionOutcomesBuilder outcomesBuilder;
    @Resource
    PredictionParticipantService participantService;
    @Resource
    PredictionTransactionsBuilder predictionTransactionsBuilder;
    @Resource
    private PredictionPositionsBuilder positionsBuilder;
    @Autowired
    private PredictionVotesBuilder predictionVotesBuilder;
    @Resource
    ProfileService profileService;
    @Resource
    PredictionMarketProperties predictionMarketProperties;

    @Override
    public PredictionMarketDTO getMarketDetail(String conditionId, String customerId) throws BaseException {
        // 获取市场记录
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketDO == null) {
            log.error("Market not found: {}", conditionId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 1. 获取市场的outcomes
        List<PredictionOutcomesDO> marketOutcomes = outcomesBuilder.getEffectiveRows(conditionId);

        // 2. 按照volume转成BigDecimal做倒排序，当volume相同时按照created倒排
        marketOutcomes = outcomesSort(marketOutcomes);
        List<String> customerIds = new ArrayList<>();
        customerIds.addAll(marketOutcomes.stream().map(PredictionOutcomesDO::getCustomerId).distinct().toList());
        customerIds.add(marketDO.getCustomerId());
        if (StringUtils.isNotBlank(customerId)) {
            customerIds.add(customerId);
        }
        GetAggProfileContext getAggProfileContext = new GetAggProfileContext();
        getAggProfileContext.setIdList(customerIds);
        getAggProfileContext.setNeedOpenSocialInfo(true);
        Map<String, ProfileDTO> profileDTOMap = profileService.getAggProfileMap(getAggProfileContext);
        // 3. 获取用户信息
        for (PredictionOutcomesDO outcomesDO : marketOutcomes) {
            // 根据 Oracle 类型和市场状态决定是否显示投票数据
            if (shouldShowVoteData(marketDO)) {
                Double answerVote = predictionVotesBuilder.getAnswerVote(outcomesDO.getMarketId(), outcomesDO.getSlot());
                outcomesDO.setVote(String.valueOf(answerVote));
            }
            Double myVote = predictionVotesBuilder.getAnswerVoteByCustomerId(outcomesDO.getMarketId(), outcomesDO.getSlot(), customerId);
            if (profileDTOMap.get(outcomesDO.getCustomerId()) != null && profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial() != null) {
                outcomesDO.setHandle(profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial().getHandle());
                outcomesDO.setNickName(profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial().getNickName());
            } else {
                outcomesDO.setHandle("");
                outcomesDO.setNickName("");
            }
            outcomesDO.setMyVote(String.valueOf(myVote));
        }
        ProfileOpenSocialDTO profileOpenSocialDTO = profileDTOMap.get(marketDO.getCustomerId()) != null && profileDTOMap.get(marketDO.getCustomerId()).getOpenSocial() != null ? profileDTOMap.get(marketDO.getCustomerId()).getOpenSocial() : ProfileOpenSocialDTO.newBuilder().build();
        // 4. 获取参与状态（如果有用户ID）
        boolean hasParticipated = false;
        if (StringUtils.isNotBlank(customerId)) {
            hasParticipated = participantService.hasParticipated(customerId, conditionId);
        }
        Long winPoint = 0L;
        if (Ended.name().equals(marketDO.getStatus()) || Closed.name().equals(marketDO.getStatus())) {
            winPoint = predictionVoteService.winPoint(marketDO.getConditionId(), customerId);
        }


        // 5. 创建DTO
        PredictionMarketDTO predictionMarketDTO = PredictionModelConvert.toMarketDTO(marketDO, marketOutcomes, hasParticipated, profileOpenSocialDTO, winPoint);
        if (marketDO.getEndTime() < System.currentTimeMillis()) {
            // TODO
            predictionMarketDTO = predictionMarketDTO.toBuilder().addAllResult(predictionVoteService.getMarketVoteResult(marketDO.getConditionId()).stream().map(BigInteger::longValue).collect(Collectors.toList())).build();
            if (predictionMarketDTO.getHasParticipant()) {
                Map<String, ZeekConstants.PredictionClaimStatus> claimableStatus = queryClaimStatus(customerId, Collections.singletonList(marketDO.getConditionId()));
                if (claimableStatus != null && claimableStatus.containsKey(marketDO.getConditionId())) {
                    predictionMarketDTO = predictionMarketDTO.toBuilder().setClaimStatus(claimableStatus.get(predictionMarketDTO.getConditionId()).name()).build();
                }
            }
        }

        if (CLAIMED.name().equals(predictionMarketDTO.getClaimStatus())) {
            PredictionTransactionsDO predictionTransactionsDO = predictionTransactionsBuilder.queryClaimByCustomerId(customerId, marketDO.getConditionId());
            predictionMarketDTO = predictionMarketDTO.toBuilder().setClaimValue(predictionTransactionsDO.getTotalValue()).build();
        }

        boolean hasWhitelist = predictionMarketProperties.getOperateWhitelist().contains(StringUtils.lowerCase(profileOpenSocialDTO.getAddress()));
        predictionMarketDTO = predictionMarketDTO.toBuilder().setHasWhitelist(hasWhitelist).build();
        return predictionMarketDTO;
    }

    private static @NotNull List<PredictionOutcomesDO> outcomesSort(List<PredictionOutcomesDO> marketOutcomes) {
        if (CollectionUtils.isEmpty(marketOutcomes)) {
            return marketOutcomes;
        }

        try {
            return marketOutcomes.stream()
                    .sorted(
                        // 1. 先按照 status 排序（倒序）
                        Comparator.comparing(
                            (PredictionOutcomesDO outcome) -> Objects.requireNonNullElse(outcome.getStatus(), 0),
                            Comparator.nullsLast(Integer::compareTo).reversed()
                        )
                        // 2. 然后按照 chance 排序（倒序）
                        .thenComparing(
                            outcome -> Objects.requireNonNullElse(outcome.getChance(), 0.0),
                            Comparator.nullsLast(Double::compareTo).reversed()
                        )
                        // 3. 然后按照 volume 排序（倒序）
                        .thenComparing(
                            outcome -> outcome.getVolumeDecimal(),
                            Comparator.nullsLast(BigDecimal::compareTo).reversed()
                        )
                        // 4. 最后按 created 排序（倒序）
                        .thenComparing(
                            outcome -> Objects.requireNonNullElse(outcome.getCreated(), 0L),
                            Comparator.nullsLast(Long::compareTo).reversed()
                        )
                    )
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error occurred while sorting outcomes, returning original list: {}", e.getMessage(), e);
            return marketOutcomes;
        }
    }

    @Override
    public PredictionMarketListDTO markets(MarketsRequest request) throws BaseException {
        PredictionMarketListDTO.Builder builder = PredictionMarketListDTO.newBuilder();
        TokenPage<PredictionMarketsDO> tokenPage = new TokenPage<PredictionMarketsDO>();
        if (request.getType().equals(MarketListType.All)) {
            tokenPage = predictionMarketsBuilder.pageSearch(request.getLimit(), request.getNextToken(), request.getStatus());
        } else if (request.getType().equals(MarketListType.Participant)) {
            List<String> marketIds = predictionParticipantsBuilder.participantsPage(request.getCustomerId(), request.getNextToken(), request.getLimit());
            List<PredictionMarketsDO> predictionMarketsDOS = predictionMarketsBuilder.getByMarketIds(marketIds);

            // 按照marketIds列表的顺序对predictionMarketsDOS进行排序
            Map<String, PredictionMarketsDO> marketMap = predictionMarketsDOS.stream()
                    .collect(Collectors.toMap(PredictionMarketsDO::getConditionId, market -> market));

            List<PredictionMarketsDO> sortedMarkets = marketIds.stream()
                    .map(marketMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            tokenPage.setRows(sortedMarkets);
        }
        // 设置下一页token
        if (tokenPage.getNextToken() != null) {
            builder.setNextToken(tokenPage.getNextToken());
        }

        // 转换市场列表
        if (tokenPage.getRows() != null && !tokenPage.getRows().isEmpty()) {
            List<PredictionMarketsDO> marketsList = tokenPage.getRows();
            // 1. 收集所有市场ID和创建者ID
            List<String> marketIds = new ArrayList<>();
            List<String> creatorIds = new ArrayList<>();
            List<String> claimStatusMarketIds = new ArrayList<>();
            // 批量查询用户参与状态
            Map<String, Boolean> participationStatus = new HashMap<>();
            String requestUserId = request.getCustomerId();
            for (PredictionMarketsDO market : marketsList) {
                marketIds.add(market.getConditionId());
                creatorIds.add(market.getCustomerId());
                if (requestUserId != null && !requestUserId.isEmpty()) {
                    String marketId = market.getConditionId();
                    if (participantService.hasParticipated(requestUserId, marketId)) {
                        participationStatus.put(marketId, true);
                        if (Closed.name().equals(market.getStatus())) {
                            claimStatusMarketIds.add(marketId);
                        }
                    } else {
                        participationStatus.put(marketId, false);
                    }
                }
            }

            // 2. 批量查询所有市场的outcomes
            List<PredictionOutcomesDO> allOutcomes = outcomesBuilder.getEffectiveRows(marketIds);

            // 3. 按市场ID分组outcomes
            Map<String, List<PredictionOutcomesDO>> outcomesByMarketId = allOutcomes.stream()
                    .collect(Collectors.groupingBy(PredictionOutcomesDO::getMarketId));

            // 4. 批量查询所有创建者的基本信息
            GetAggProfileContext getAggProfileContext = new GetAggProfileContext();
            getAggProfileContext.setIdList(creatorIds);
            getAggProfileContext.setNeedOpenSocialInfo(true);
            Map<String, ProfileDTO> creatorProfiles = profileService.getAggProfileMap(getAggProfileContext);


            Map<String, ZeekConstants.PredictionClaimStatus> claimableStatus = queryClaimStatus(request.getCustomerId(), claimStatusMarketIds);

            // 6. 构建DTO列表
            List<PredictionMarketDTO> marketDTOList = new ArrayList<>();
            for (PredictionMarketsDO market : marketsList) {
                String marketId = market.getConditionId();
                String creatorId = market.getCustomerId();
                // 获取该市场的outcomes
                List<PredictionOutcomesDO> marketOutcomes = outcomesByMarketId.getOrDefault(marketId, Collections.emptyList());
                // 按照volume转成BigDecimal做倒排序，当volume相同时按照created倒排
                marketOutcomes = outcomesSort(marketOutcomes);
                // 获取创建者信息
                ProfileDTO profileDTO = creatorProfiles.getOrDefault(creatorId, ProfileDTO.newBuilder().build());
                // 获取参与状态
                boolean hasParticipated = participationStatus.getOrDefault(marketId, false);
                // 创建DTO
                PredictionMarketDTO dto = PredictionModelConvert.toMarketDTO(market, marketOutcomes, hasParticipated, profileDTO.getOpenSocial() == null ? ProfileOpenSocialDTO.newBuilder().build() : profileDTO.getOpenSocial());
                if (claimableStatus.containsKey(marketId)) {
                    dto = dto.toBuilder().setClaimStatus(claimableStatus.get(marketId).name()).build();
                }
                marketDTOList.add(dto);
            }
            builder.addAllPredictionMarketDTOs(marketDTOList);
        }
        return builder.build();
    }

    @Override
    public boolean updateFunding(String conditionId, BigInteger funding, BigInteger timestamp) {
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketsDO == null) {
            log.warn("market not exist, conditionId: {}", conditionId);
            return false;
        }

        if (marketsDO.getEventTime() != null && marketsDO.getEventTime() >= timestamp.longValue()) {
            log.warn("market funding already updated, conditionId: {}, eventTime: {}, timestamp: {}", conditionId, marketsDO.getEventTime(), timestamp);
            return true;
        }
        log.info("market funding update, conditionId: {}, oldFunding: {}, eventTime: {}, funding: {}, timestamp: {}",
                conditionId, marketsDO.getFunding(), marketsDO.getEventTime(), funding, timestamp);

        return predictionMarketsBuilder.updateFunding(marketsDO, funding.toString(), timestamp.longValue());
    }

    @Override
    public boolean feeChanged(MarketAMMFeeChangedEventLog event) throws BaseException {
        log.info("market feeChanged: {}", event);
        PredictionMarketsDO predictionMarketsDO = predictionMarketsBuilder.getByMarketAddress(event.getMarketAddress());
        predictionMarketsDO.setFee(String.valueOf(event.getNewFee()));
        predictionMarketsDO.setCreatorFee(String.valueOf(event.getNewOwnerFee()));
        predictionMarketsDO.setProposalFee(String.valueOf(event.getNewProposalFee()));
        return predictionMarketsBuilder.updateRow(predictionMarketsDO);
    }

    private Map<String, ZeekConstants.PredictionClaimStatus> queryClaimStatus(String customerId, List<String> marketIds) throws BaseException {
        Map<String, ZeekConstants.PredictionClaimStatus> result = Maps.newHashMap();
        if (StringUtils.isBlank(customerId) || CollectionUtils.isEmpty(marketIds)) {
            return result;
        }

        // 查询用户已经 claim 的市场，如果已经 claim 直接返回 PredictionClaimStatus.CLAIMED
        List<PredictionTransactionsDO> allTransactionsDOS = predictionTransactionsBuilder.queryByCustomerId(customerId, marketIds);

        for (String marketId : marketIds) {
            // 过滤得到  marketId 的数据
            List<PredictionTransactionsDO> marketTransactionsDOS = allTransactionsDOS.stream().filter(t -> t.getMarketId().equals(marketId)).collect(Collectors.toList());

            // 如果存在一条 action 为 CLAIM，则返回 PredictionClaimStatus.CLAIMED
            if (marketTransactionsDOS.stream().anyMatch(t -> t.getAction() == ZeekConstants.PredictionAction.CLAIM.getCode())) {
                result.put(marketId, CLAIMED);
                continue;
            }

            // 如果存在一条 action 为 POST，则返回 PredictionClaimStatus.CLAIMABLE
            if (marketTransactionsDOS.stream().anyMatch(t -> t.getAction() == ZeekConstants.PredictionAction.POST.getCode())) {
                result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                continue;
            }

            // 如果用户 OUTCOME 过，且该条 OUTCOME 存在 TRADE 的数据，则返回 PredictionClaimStatus.CLAIMABLE
            PredictionTransactionsDO outcomeTransaction = marketTransactionsDOS.stream().filter(t -> t.getAction() == ZeekConstants.PredictionAction.OUTCOME.getCode()).findFirst().orElse(null);
            if (outcomeTransaction != null) {
                if (marketTransactionsDOS.stream().anyMatch(t -> t.getSlot().equals(outcomeTransaction.getSlot()) && t.getAction() == ZeekConstants.PredictionAction.TRADE.getCode())) {
                    result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                    continue;
                }
            }

            // 获取该 market 下所有的 outcomes
            List<PredictionOutcomesDO> outcomes = outcomesBuilder.getEffectiveRows(marketId);
            // 如果 outcomes 中存在 customerId 的数据，且 status = PREFECT，则返回 PredictionClaimStatus.CLAIMABLE
            if (outcomes.stream().anyMatch(o -> o.getCustomerId().equals(customerId) && o.getStatus() == ZeekConstants.OutcomeStatus.PERFECT.getCode())) {
                result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                continue;
            }

            // 获取用户当前市场的持仓，如果存在 outcome = PEREFECT，对应 outcome 的持仓还大于 0 ，则返回 PredictionClaimStatus.CLAIMABLE
            List<Integer> perfectSlots = outcomes.stream().filter(o -> o.getStatus() == ZeekConstants.OutcomeStatus.PERFECT.getCode()).map(PredictionOutcomesDO::getSlot).collect(Collectors.toList());
            List<PredictionPositionsDO> positionsDOS = positionsBuilder.queryByMarketId(customerId, marketId);
            if (CollectionUtils.isNotEmpty(positionsDOS)) {
                if (positionsDOS.stream().anyMatch(p -> perfectSlots.contains(p.getSlot()) && new BigInteger(p.getValue()).compareTo(BigInteger.ZERO) > 0)) {
                    result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                }
            }

            // 如果上边都不满足，则返回 PredictionClaimStatus.NONE
            result.put(marketId, ZeekConstants.PredictionClaimStatus.NONE);
        }

        log.info("queryClaimStatus: customerId:{}, result:{}", customerId, JSON.toJSONString(result));
        return result;
    }

    /**
     * 判断是否应该显示投票数据
     *
     * @param marketDO 市场数据
     * @return 是否显示投票数据
     */
    private static boolean shouldShowVoteData(PredictionMarketsDO marketDO) {
        // 检查 Oracle 类型
        ZeekConstants.PredictionOracleType oracleType = marketDO.getOracleType();

        try {
            switch (oracleType) {
                case Vote:
                    // 投票类型：只有市场结束后才显示投票数据
                    return marketDO.getEndTime() < System.currentTimeMillis();

                case Manual:
                    // 手动类型：总是显示投票数据（用于参考）
                    return true;

                default:
                    // 未知类型：使用默认逻辑
                    return marketDO.getEndTime() < System.currentTimeMillis();
            }
        } catch (IllegalArgumentException e) {
            log.warn("Invalid Oracle type: {}, using default logic", oracleType);
            return marketDO.getEndTime() < System.currentTimeMillis();
        }
    }

}
