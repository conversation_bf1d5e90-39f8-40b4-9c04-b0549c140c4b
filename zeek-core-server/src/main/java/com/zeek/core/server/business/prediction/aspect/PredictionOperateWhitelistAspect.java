package com.zeek.core.server.business.prediction.aspect;

import com.zeek.core.api.CloseMarketRequest;
import com.zeek.core.api.CreatMarketRequest;
import com.zeek.core.api.PredictionOutcomesRequest;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.model.ProfileBasicDO;
import com.zeek.core.server.business.prediction.annotation.PredictionOperateWhitelist;
import com.zeek.core.server.business.profile.service.ProfileService;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 预测市场操作白名单权限控制切面
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Aspect
@Component
public class PredictionOperateWhitelistAspect {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PredictionMarketProperties predictionMarketProperties;

    @Around("@annotation(predictionOperateWhitelist)")
    public Object checkWhitelist(ProceedingJoinPoint joinPoint, PredictionOperateWhitelist predictionOperateWhitelist) throws Throwable {
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            String customerId = extractCustomerId(args);
            
            if (StringUtils.isBlank(customerId)) {
                log.warn("无法从方法参数中提取 customerId，方法：{}", joinPoint.getSignature().getName());
                throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
            }

            // 通过 ProfileService 获取用户的 address
            ProfileBasicDO profileBasic = profileService.getProfileBasic(customerId);
            if (profileBasic == null || StringUtils.isBlank(profileBasic.getAddress())) {
                log.warn("用户 {} 的地址信息不存在", customerId);
                throw new BaseException(ZeekCoreRespondCode.USER_BASIC_NOT_FOUND);
            }

            String userAddress = profileBasic.getAddress().toLowerCase();
            
            // 检查是否在白名单中
            List<String> operateWhitelist = predictionMarketProperties.getOperateWhitelist();
            if (!operateWhitelist.contains(userAddress)) {
                log.warn("用户 {} (地址: {}) 不在操作白名单中，操作被拒绝。方法：{}", 
                    customerId, userAddress, joinPoint.getSignature().getName());
                throw new BaseException(ZeekCoreRespondCode.OPERATION_NOT_ALLOWED);
            }

            log.info("用户 {} (地址: {}) 通过白名单验证，允许执行操作：{}", 
                customerId, userAddress, joinPoint.getSignature().getName());
            
            // 执行原方法
            return joinPoint.proceed();
            
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("白名单权限检查过程中发生异常", e);
            throw new BaseException(ZeekCoreRespondCode.SYSTEM_ERROR);
        }
    }

    /**
     * 从方法参数中提取 customerId
     * 
     * @param args 方法参数数组
     * @return customerId
     */
    private String extractCustomerId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CreatMarketRequest) {
                return ((CreatMarketRequest) arg).getCustomerId();
            } else if (arg instanceof CloseMarketRequest) {
                return ((CloseMarketRequest) arg).getCustomerId();
            } else if (arg instanceof PredictionOutcomesRequest) {
                return ((PredictionOutcomesRequest) arg).getCustomerId();
            }
        }
        
        return null;
    }
}
