package com.zeek.core.server.business.prediction.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Prediction market operation limit annotation
 * Used to control permissions for creating markets, closing markets, creating outcomes, etc.
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PredictionLimit {

    /**
     * Operation type to identify different scenarios
     * @return operation type
     */
    Type type() default Type.DEFAULT;

    /**
     * Custom error message
     * @return error message
     */
    String message() default "Operation not allowed, user not in operation whitelist";

    /**
     * Operation type enum
     */
    enum Type {
        DEFAULT,        // Default scenario
        CREATE_MARKET,  // Create market scenario
        CLOSE_MARKET,   // Close market scenario
        CREATE_OUTCOME  // Create outcome scenario
    }
}
