package com.zeek.core.server.business.prediction.aspect;

import com.zeek.core.api.CloseMarketRequest;
import com.zeek.core.api.CreatMarketRequest;
import com.zeek.core.api.PredictionOutcomesRequest;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.PredictionMarketsBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.ProfileBasicDO;
import com.zeek.core.server.business.prediction.annotation.PredictionLimit;
import com.zeek.core.server.business.profile.service.ProfileService;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Prediction market operation whitelist permission control aspect
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Aspect
@Component
public class PredictionLimitAspect {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PredictionMarketProperties predictionMarketProperties;

    @Autowired
    private PredictionMarketsBuilder predictionMarketsBuilder;

    @Around("@annotation(predictionLimit)")
    public Object checkWhitelist(ProceedingJoinPoint joinPoint, PredictionLimit predictionLimit) throws Throwable {
        try {
            // Get annotation type and method parameters
            PredictionLimit.Type operationType = predictionLimit.type();
            Object[] args = joinPoint.getArgs();
            String customerId = extractCustomerId(args);
            String marketId = extractMarketId(args);



            // Execute original method
            return joinPoint.proceed();

        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Exception occurred during whitelist permission check", e);
            throw new BaseException(ZeekCoreRespondCode.SYSTEM_ERROR);
        }
    }

    /**
     * Extract customerId from method parameters
     *
     * @param args method parameter array
     * @return customerId
     */
    private String extractCustomerId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CreatMarketRequest) {
                return ((CreatMarketRequest) arg).getCustomerId();
            } else if (arg instanceof CloseMarketRequest) {
                return ((CloseMarketRequest) arg).getCustomerId();
            } else if (arg instanceof PredictionOutcomesRequest) {
                return ((PredictionOutcomesRequest) arg).getCustomerId();
            }
        }

        return null;
    }

    /**
     * Extract marketId from method parameters
     *
     * @param args method parameter array
     * @return marketId
     */
    private String extractMarketId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CloseMarketRequest) {
                return ((CloseMarketRequest) arg).getMarketId();
            } else if (arg instanceof PredictionOutcomesRequest) {
                return ((PredictionOutcomesRequest) arg).getMarketId();
            }
            // CreatMarketRequest doesn't have marketId as it's creating a new market
        }

        return null;
    }
}
