package com.zeek.core.server.business.prediction.aspect;

import com.zeek.core.api.CloseMarketRequest;
import com.zeek.core.api.CreatMarketRequest;
import com.zeek.core.api.PredictionOutcomesRequest;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.PredictionMarketsBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.ProfileBasicDO;
import com.zeek.core.server.business.prediction.annotation.PredictionLimit;
import com.zeek.core.server.business.profile.service.ProfileService;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Prediction market operation whitelist permission control aspect
 * 
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Aspect
@Component
public class PredictionLimitAspect {

    @Autowired
    private ProfileService profileService;

    @Autowired
    private PredictionMarketProperties predictionMarketProperties;

    @Autowired
    private PredictionMarketsBuilder predictionMarketsBuilder;

    @Around("@annotation(predictionLimit)")
    public Object checkWhitelist(ProceedingJoinPoint joinPoint, PredictionLimit predictionLimit) throws Throwable {
        try {
            // Get annotation type and method parameters
            PredictionLimit.Type operationType = predictionLimit.type();
            Object[] args = joinPoint.getArgs();
            String customerId = extractCustomerId(args);

            log.info("Processing prediction limit check for operation type: {}, method: {}",
                operationType, joinPoint.getSignature().getName());

            if (StringUtils.isBlank(customerId)) {
                log.warn("Unable to extract customerId from method parameters, operation type: {}, method: {}",
                    operationType, joinPoint.getSignature().getName());
                throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
            }

            ZeekConstants.PredictionOracleType oracleType = null;

            // Determine oracle type based on operation type
            if (operationType == PredictionLimit.Type.CREATE_MARKET) {
                // For CREATE_MARKET: get oracleType from request parameters
                String requestOracleType = extractOracleType(args);
                if (StringUtils.isBlank(requestOracleType)) {
                    // If oracleType is null, use default from configuration
                    oracleType = predictionMarketProperties.getOracle().getType();
                } else {
                    oracleType = ZeekConstants.PredictionOracleType.valueOf(requestOracleType);
                }
                log.info("CREATE_MARKET operation, oracle type: {}, operation type: {}, method: {}",
                    oracleType, operationType, joinPoint.getSignature().getName());
            } else if (operationType == PredictionLimit.Type.CLOSE_MARKET || operationType == PredictionLimit.Type.CREATE_OUTCOME) {
                // For CLOSE_MARKET and CREATE_OUTCOME: get oracleType from market data
                String marketId = extractMarketId(args);
                if (StringUtils.isBlank(marketId)) {
                    log.warn("Unable to extract marketId from method parameters, operation type: {}, method: {}",
                        operationType, joinPoint.getSignature().getName());
                    throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
                }

                PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(marketId);
                if (marketDO == null) {
                    log.warn("Market {} not found, operation type: {}, method: {}",
                        marketId, operationType, joinPoint.getSignature().getName());
                    throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
                }

                oracleType = marketDO.getOracleType();
                log.info("Market {} oracle type: {}, operation type: {}, method: {}",
                    marketId, oracleType, operationType, joinPoint.getSignature().getName());
            }

            // Only check whitelist for Manual oracle type
            if (oracleType != ZeekConstants.PredictionOracleType.Manual) {
                log.info("Oracle type is {}, skipping whitelist check. Operation type: {}, Method: {}",
                    oracleType, operationType, joinPoint.getSignature().getName());
                return joinPoint.proceed();
            }

            log.info("Oracle type is Manual, proceeding with whitelist check. Operation type: {}, Method: {}",
                operationType, joinPoint.getSignature().getName());

            // Get user address through ProfileService
            ProfileBasicDO profileBasic = profileService.getProfileBasic(customerId);
            if (profileBasic == null || StringUtils.isBlank(profileBasic.getAddress())) {
                log.warn("User {} address information does not exist, operation type: {}", customerId, operationType);
                throw new BaseException(ZeekCoreRespondCode.USER_BASIC_NOT_FOUND);
            }

            String userAddress = profileBasic.getAddress().toLowerCase();

            // Check if in whitelist
            List<String> operateWhitelist = predictionMarketProperties.getOperateWhitelist();
            if (!operateWhitelist.contains(userAddress)) {
                log.warn("User {} (address: {}) is not in operation whitelist, operation denied. Operation type: {}, Method: {}",
                    customerId, userAddress, operationType, joinPoint.getSignature().getName());
                throw new BaseException(ZeekCoreRespondCode.OPERATION_NOT_ALLOWED);
            }

            log.info("User {} (address: {}) passed whitelist verification, operation allowed. Operation type: {}, Method: {}",
                customerId, userAddress, operationType, joinPoint.getSignature().getName());

            // Execute original method
            return joinPoint.proceed();

        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Exception occurred during whitelist permission check", e);
            throw new BaseException(ZeekCoreRespondCode.SYSTEM_ERROR);
        }
    }

    /**
     * Extract customerId from method parameters
     *
     * @param args method parameter array
     * @return customerId
     */
    private String extractCustomerId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CreatMarketRequest) {
                return ((CreatMarketRequest) arg).getCustomerId();
            } else if (arg instanceof CloseMarketRequest) {
                return ((CloseMarketRequest) arg).getCustomerId();
            } else if (arg instanceof PredictionOutcomesRequest) {
                return ((PredictionOutcomesRequest) arg).getCustomerId();
            }
        }

        return null;
    }

    /**
     * Extract marketId from method parameters
     *
     * @param args method parameter array
     * @return marketId
     */
    private String extractMarketId(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CloseMarketRequest) {
                return ((CloseMarketRequest) arg).getMarketId();
            } else if (arg instanceof PredictionOutcomesRequest) {
                return ((PredictionOutcomesRequest) arg).getMarketId();
            }
            // CreatMarketRequest doesn't have marketId as it's creating a new market
        }

        return null;
    }

    /**
     * Extract oracleType from method parameters
     *
     * @param args method parameter array
     * @return oracleType
     */
    private String extractOracleType(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }

        for (Object arg : args) {
            if (arg instanceof CreatMarketRequest) {
                return ((CreatMarketRequest) arg).getOracleType();
            }
        }

        return null;
    }
}
