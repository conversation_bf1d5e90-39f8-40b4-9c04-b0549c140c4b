syntax = "proto3";

package com.zeek.core.api;
option java_package = "com.zeek.core.api";
option java_multiple_files = true;
import "Base.proto"; // 导入 Google Timestamp 类型

message VampireAttackRequestDTO {
  string customerId = 1;
}

message PriceDTO {
  string symbol = 1;
  string quote = 2;
  string price = 3;
}

message SignRequest {
  string data = 1;
}

message SignResponse {
  string signature = 1;
}

// 小文件上传请求（< 4MB，使用Base64编码）
message FileUploadRequest {
  string customerId = 1;           // 用户ID
  string fileContent = 2;          // Base64编码的文件内容
  string fileName = 3;             // 文件名
  string contentType = 4;          // MIME类型
  int64 fileSize = 5;              // 文件大小（字节）
  string category = 6;             // 文件分类（avatar, document, image等）
  map<string, string> metadata = 7; // 额外元数据
}

// 文件上传响应
message FileUploadResponse {
  string code = 1;                 // 响应码
  string message = 2;              // 响应消息
  string fileId = 3;               // 文件唯一标识
  string fileUrl = 4;              // 文件访问URL
  AttachmentDTO attachment = 5;    // 文件信息
}

// 大文件上传初始化请求
message InitLargeFileUploadRequest {
  string customerId = 1;
  string fileName = 2;
  string contentType = 3;
  int64 totalSize = 4;             // 文件总大小
  string category = 5;
  map<string, string> metadata = 6;
}

// 大文件上传初始化响应
message InitLargeFileUploadResponse {
  string code = 1;
  string message = 2;
  string uploadId = 3;             // 上传会话ID
  int32 chunkSize = 4;             // 建议的分块大小（默认1MB）
  repeated string uploadUrls = 5;   // 预签名上传URL列表（用于直接上传到OSS）
}

// 分块上传请求
message ChunkUploadRequest {
  string uploadId = 1;             // 上传会话ID
  int32 chunkIndex = 2;            // 分块索引（从0开始）
  string chunkContent = 3;         // Base64编码的分块数据
  string checksum = 4;             // 分块MD5校验和
}

// 分块上传响应
message ChunkUploadResponse {
  string code = 1;
  string message = 2;
  bool success = 3;
  int32 chunkIndex = 4;
  string etag = 5;                 // 分块ETag（用于最终合并）
}

// 完成大文件上传请求
message CompleteLargeFileUploadRequest {
  string uploadId = 1;
  repeated string etags = 2;       // 所有分块的ETag
  string finalChecksum = 3;        // 整个文件的MD5校验和
}

// 完成大文件上传响应
message CompleteLargeFileUploadResponse {
  string code = 1;
  string message = 2;
  string fileId = 3;
  string fileUrl = 4;
  AttachmentDTO attachment = 5;
}

service RemoteCommonService {
  rpc vampireAttack (VampireAttackRequestDTO) returns (CommonResult);

  rpc getPrice(PriceDTO) returns (CommonResult);

  rpc signPrefixedMessage (SignRequest) returns (CommonResult);

  // 小文件上传（< 4MB）
  rpc uploadFile (FileUploadRequest) returns (FileUploadResponse);

  // 大文件上传初始化
  rpc initLargeFileUpload (InitLargeFileUploadRequest) returns (InitLargeFileUploadResponse);

  // 分块上传
  rpc uploadChunk (ChunkUploadRequest) returns (ChunkUploadResponse);

  // 完成大文件上传
  rpc completeLargeFileUpload (CompleteLargeFileUploadRequest) returns (CompleteLargeFileUploadResponse);

}