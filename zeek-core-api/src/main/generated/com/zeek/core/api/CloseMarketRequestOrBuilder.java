// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

public interface CloseMarketRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zeek.core.api.CloseMarketRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string marketId = 1;</code>
   * @return The marketId.
   */
  java.lang.String getMarketId();
  /**
   * <code>string marketId = 1;</code>
   * @return The bytes for marketId.
   */
  com.google.protobuf.ByteString
      getMarketIdBytes();

  /**
   * <code>int32 slot = 2;</code>
   * @return The slot.
   */
  int getSlot();

  /**
   * <code>string customerId = 3;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <code>string customerId = 3;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();
}
